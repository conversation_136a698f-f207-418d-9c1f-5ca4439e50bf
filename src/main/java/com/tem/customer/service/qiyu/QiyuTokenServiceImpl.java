package com.tem.customer.service.qiyu;

import com.iplatform.common.utils.LogUtils;
import com.tem.customer.model.dto.qiyu.QiyuTokenResponse;
import com.tem.customer.shared.utils.QiyuCrmSignatureUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.Serial;
import java.io.Serializable;
import java.time.Duration;
import java.time.LocalDateTime;

/**
 * 七鱼Token管理服务
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Slf4j
@Service("qiyuTokenService")
public class QiyuTokenServiceImpl implements QiyuTokenService {

    /**
     * Redisson客户端
     */
    private final RedissonClient redissonClient;

    public QiyuTokenServiceImpl(@Qualifier("businessRedissonClient") RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    /**
     * Token缓存Key前缀
     */
    private static final String TOKEN_CACHE_KEY = "qiyu:crm:token:cache";

    /**
     * 七鱼AppId
     */
    @Value("${qiyu.crm.app.key}")
    private String appKey;

    /**
     * 七鱼AppSecret
     */
    @Value("${qiyu.crm.app.secret}")
    private String appSecret;

    /**
     * Token有效期（分钟）
     */
    @Value("${qiyu.crm.token.timeout:120}")
    private int tokenTimeoutMinutes;

    /**
     * 获取Token的Redis Key
     *
     * @param token Token值
     * @return Redis Key
     */
    private String getTokenKey(String token) {
        return TOKEN_CACHE_KEY + ":" + token;
    }

    /**
     * 获取Token
     *
     * @param requestAppId     请求的AppId
     * @param requestAppSecret 请求的AppSecret
     * @return Token响应
     */
    public QiyuTokenResponse getToken(String requestAppId, String requestAppSecret) {
        try {
            LogUtils.info(log, "开始获取七鱼CRM Token，AppId: {}", requestAppId);

            // 验证AppId和AppSecret
            if (!QiyuCrmSignatureUtil.validateAppCredentials(requestAppId, requestAppSecret, appKey, appSecret)) {
                return QiyuTokenResponse.error(1, "Invalid appid or appsecret");
            }

            // 生成Token
            long timestamp = System.currentTimeMillis();
            String token = QiyuCrmSignatureUtil.generateToken(requestAppId, timestamp);

            if (token == null) {
                return QiyuTokenResponse.systemError("Failed to generate token");
            }

            // 计算过期时间（毫秒）
            long expiresMillis = tokenTimeoutMinutes * 60 * 1000L;
            LocalDateTime expireTime = LocalDateTime.now().plusMinutes(tokenTimeoutMinutes);

            // 缓存Token到Redis，使用单独的Key为每个Token设置过期时间
            TokenInfo tokenInfo = new TokenInfo(token, expireTime, requestAppId);
            String tokenKey = getTokenKey(token);
            redissonClient.getBucket(tokenKey).set(tokenInfo, Duration.ofMinutes(tokenTimeoutMinutes));

            LogUtils.info(log, "成功生成七鱼CRM Token: {}, 过期时间: {}", token, expireTime);
            return QiyuTokenResponse.success(token, expiresMillis);

        } catch (Exception e) {
            LogUtils.error(log, "获取七鱼CRM Token异常", e);
            return QiyuTokenResponse.systemError("System error");
        }
    }

    /**
     * 验证Token是否有效
     *
     * @param token Token值
     * @return 验证结果
     */
    @Override
    public boolean validateToken(String token) {
        if (token == null || token.trim().isEmpty()) {
            return false;
        }

        String tokenKey = getTokenKey(token);
        TokenInfo tokenInfo = (TokenInfo) redissonClient.getBucket(tokenKey).get();
        if (tokenInfo == null) {
            LogUtils.warn(log, "七鱼CRM Token不存在: {}", token);
            return false;
        }

        if (LocalDateTime.now().isAfter(tokenInfo.expireTime())) {
            redissonClient.getBucket(tokenKey).delete();
            LogUtils.warn(log, "七鱼CRM Token已过期: {}", token);
            return false;
        }

        return true;
    }


    /**
     * Token信息
     */
    public record TokenInfo(String token, LocalDateTime expireTime, String appId) implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

    }
}
