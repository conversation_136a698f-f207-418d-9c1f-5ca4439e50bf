package com.tem.customer.service.auth;

import com.iplatform.common.utils.LogUtils;
import com.wf.captcha.SpecCaptcha;
import com.wf.captcha.base.Captcha;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.time.Duration;

/**
 * 图形验证码服务
 * 提供验证码生成和验证功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service("captchaService")
public class CaptchaService {

    /**
     * 验证码宽度
     */
    private static final int WIDTH = 120;

    /**
     * 验证码高度
     */
    private static final int HEIGHT = 40;

    /**
     * 验证码字符长度
     */
    private static final int CODE_LENGTH = 4;

    /**
     * 验证码过期时间（5分钟）
     */
    private static final long EXPIRE_TIME = 5 * 60;



    /**
     * Redis key前缀
     */
    private static final String CAPTCHA_KEY_PREFIX = "captcha:";

    /**
     * 图形验证码获取频率限制key前缀
     */
    private static final String CAPTCHA_RATE_LIMIT_KEY_PREFIX = "captcha:rate_limit:";

    /**
     * 短信验证码发送频率限制key前缀
     */
    private static final String SMS_RATE_LIMIT_KEY_PREFIX = "sms:rate_limit:";

    /**
     * 图形验证码获取频率限制（60秒内最多10次）
     */
    private static final int CAPTCHA_MAX_REQUESTS_PER_MINUTE = 10;
    private static final long CAPTCHA_RATE_LIMIT_WINDOW = 60;

    /**
     * 短信验证码发送频率限制（60秒间隔）
     */
    private static final long SMS_RATE_LIMIT_INTERVAL = 60; // 60秒间隔

    @Autowired
    @Qualifier("businessRedissonClient")
    private RedissonClient redissonClient;

    /**
     * 生成图形验证码
     *
     * @param captchaKey 验证码key
     * @return base64编码的图片
     */
    public String generateCaptchaImage(String captchaKey) {
        try {
            // 使用EasyCaptcha生成验证码
            SpecCaptcha captcha = new SpecCaptcha(WIDTH, HEIGHT, CODE_LENGTH);
            captcha.setCharType(Captcha.TYPE_DEFAULT);

            // 获取验证码文本和图片
            String captchaText = captcha.text();
            String base64Image = captcha.toBase64();

            // 存储验证码到Redis
            storeCaptchaText(captchaKey, captchaText);

            LogUtils.info(log, "生成图形验证码: captchaKey={}, text={}", captchaKey, captchaText);

            return base64Image;

        } catch (Exception e) {
            LogUtils.error(log, "生成图形验证码异常: captchaKey=" + captchaKey, e);
            throw new RuntimeException("生成验证码失败", e);
        }
    }

    /**
     * 验证图形验证码
     *
     * @param captchaKey 验证码key
     * @param userInput  用户输入的验证码
     * @return 验证结果
     */
    public boolean validateCaptcha(String captchaKey, String userInput) {
        try {
            if (captchaKey == null || userInput == null) {
                return false;
            }

            // 从Redis获取验证码
            String storedCaptcha = getCaptchaText(captchaKey);

            if (storedCaptcha == null) {
                LogUtils.warn(log, "验证码已过期或不存在: captchaKey={}", captchaKey);
                return false;
            }

            // 验证码验证（忽略大小写）
            boolean isValid = storedCaptcha.equalsIgnoreCase(userInput.trim());

            if (isValid) {
                // 验证成功后删除验证码（一次性使用）
                deleteCaptcha(captchaKey);
                LogUtils.info(log, "图形验证码验证成功: captchaKey={}", captchaKey);
            } else {
                LogUtils.warn(log, "图形验证码验证失败: captchaKey={}, expected={}, actual={}",
                        captchaKey, storedCaptcha, userInput);
            }

            return isValid;

        } catch (Exception e) {
            LogUtils.error(log, "验证图形验证码异常: captchaKey=" + captchaKey, e);
            return false;
        }
    }





    /**
     * 存储验证码到Redis
     */
    private void storeCaptchaText(String captchaKey, String captchaText) {
        String redisKey = CAPTCHA_KEY_PREFIX + captchaKey;
        RBucket<String> bucket = redissonClient.getBucket(redisKey);
        bucket.set(captchaText, Duration.ofSeconds(EXPIRE_TIME));
    }

    /**
     * 从Redis获取验证码
     */
    private String getCaptchaText(String captchaKey) {
        String redisKey = CAPTCHA_KEY_PREFIX + captchaKey;
        RBucket<String> bucket = redissonClient.getBucket(redisKey);
        return bucket.get();
    }

    /**
     * 删除验证码
     */
    private void deleteCaptcha(String captchaKey) {
        String redisKey = CAPTCHA_KEY_PREFIX + captchaKey;
        RBucket<String> bucket = redissonClient.getBucket(redisKey);
        bucket.delete();
    }

    /**
     * 检查图形验证码获取频率限制
     * 60秒内最多10次
     *
     * @param clientIp 客户端IP地址
     * @return true-允许获取，false-超出限制
     */
    public boolean checkCaptchaRateLimit(String clientIp) {
        try {
            String rateLimitKey = CAPTCHA_RATE_LIMIT_KEY_PREFIX + clientIp;
            RBucket<String> bucket = redissonClient.getBucket(rateLimitKey);

            String currentCountStr = bucket.get();
            int currentCount = currentCountStr != null ? Integer.parseInt(currentCountStr) : 0;

            if (currentCount >= CAPTCHA_MAX_REQUESTS_PER_MINUTE) {
                LogUtils.warn(log, "图形验证码获取频率超限: clientIp={}, currentCount={}", clientIp, currentCount);
                return false;
            }

            // 增加计数
            currentCount++;
            bucket.set(String.valueOf(currentCount), Duration.ofSeconds(CAPTCHA_RATE_LIMIT_WINDOW));

            LogUtils.info(log, "图形验证码获取频率检查通过: clientIp={}, currentCount={}", clientIp, currentCount);
            return true;

        } catch (Exception e) {
            LogUtils.error(log, "检查图形验证码获取频率异常: clientIp=" + clientIp, e);
            // 异常情况下允许获取，避免影响正常用户
            return true;
        }
    }

    /**
     * 检查短信验证码发送频率限制
     * 60秒间隔限制
     *
     * @param mobile 手机号
     * @return 频率限制检查结果
     */
    public SmsRateLimitResult checkSmsRateLimit(String mobile) {
        try {
            // 检查60秒间隔限制
            String intervalKey = SMS_RATE_LIMIT_KEY_PREFIX + "interval:" + mobile;
            RBucket<String> intervalBucket = redissonClient.getBucket(intervalKey);

            if (intervalBucket.isExists()) {
                long remainingTime = intervalBucket.remainTimeToLive();
                LogUtils.warn(log, "短信发送间隔限制: mobile={}, remainingTime={}秒", mobile, remainingTime);
                return new SmsRateLimitResult(false, String.format("发送过于频繁，请%d秒后再试", remainingTime));
            }

            // 设置间隔限制
            intervalBucket.set("sent", Duration.ofSeconds(SMS_RATE_LIMIT_INTERVAL));

            LogUtils.info(log, "短信发送频率检查通过: mobile={}", mobile);
            return new SmsRateLimitResult(true, "允许发送");

        } catch (Exception e) {
            LogUtils.error(log, "检查短信发送频率异常: mobile=" + mobile, e);
            // 异常情况下允许发送，避免影响正常用户
            return new SmsRateLimitResult(true, "允许发送");
        }
    }

    /**
     * 短信频率限制检查结果
     */
    public record SmsRateLimitResult(boolean allowed, String message) {
    }
}
