package com.tem.customer.infrastructure.config;

import com.tem.platform.api.MenuService;
import com.tem.platform.api.OrgService;
import com.tem.platform.api.PartnerService;
import com.tem.platform.api.RoleAuthService;
import com.tem.platform.api.TransService;
import com.tem.platform.api.UserRoleService;
import com.tem.platform.api.UserService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.annotation.Configuration;


@Configuration
public class AppConfig {
    @DubboReference(timeout = 10000, retries = 0, check = false)
    private UserService userService;

    @DubboReference(timeout = 10000, retries = 0, check = false)
    private PartnerService partnerService;

    @DubboReference(timeout = 10000, retries = 0, check = false)
    private OrgService orgService;

    @DubboReference(timeout = 10000, retries = 0, check = false)
    private MenuService menuService;

    @DubboReference(timeout = 10000, retries = 0, check = false)
    private TransService transService;

    @DubboReference(timeout = 10000, retries = 0, check = false)
    private RoleAuthService roleAuthService;

    @DubboReference(timeout = 10000, retries = 0, check = false)
    private UserRoleService userRoleService;

  /*  @Bean
    public UserDetailLoader userDetailLoader() {
        PlatformUserDetailLoader platformUserDetailLoader = new PlatformUserDetailLoader();
        platformUserDetailLoader.setMenuService(menuService);
        platformUserDetailLoader.setOrgService(orgService);
        platformUserDetailLoader.setUserService(userService);
        platformUserDetailLoader.setUserRoleService(userRoleService);
        platformUserDetailLoader.setTransService(transService);
        platformUserDetailLoader.setRoleAuthService(roleAuthService);
        return platformUserDetailLoader;
    }

    @Bean
    public PlatformOrgLoader platformOrgLoader() {
        PlatformOrgLoader platformOrgLoader = new PlatformOrgLoader();
        platformOrgLoader.setOrgService(orgService);
        return platformOrgLoader;
    }

    @Bean
   public OrgLoaderHolder orgLoaderHolder(PlatformOrgLoader platformOrgLoader){
        OrgLoaderHolder orgLoaderHolder = new OrgLoaderHolder();
        orgLoaderHolder.setLoader(platformOrgLoader);
        return orgLoaderHolder;
    }*/


}
