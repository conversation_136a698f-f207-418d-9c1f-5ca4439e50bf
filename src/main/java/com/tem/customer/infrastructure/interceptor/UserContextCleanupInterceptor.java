package com.tem.customer.infrastructure.interceptor;

import com.iplatform.common.utils.LogUtils;
import com.tem.customer.shared.utils.ThreadLocalCleanupUtil;
import com.tem.platform.security.authorize.ContextHolder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 用户上下文清理拦截器
 * 解决 ThreadLocal 线程复用导致的用户信息混乱问题
 * 
 * <p>主要功能：</p>
 * <ul>
 *   <li>在请求开始前记录当前线程信息</li>
 *   <li>在请求完成后强制清理所有 ThreadLocal 数据</li>
 *   <li>防止线程复用时获取到上一个请求的用户信息</li>
 *   <li>提供详细的调试日志用于问题排查</li>
 * </ul>
 * 
 * <p>执行顺序：</p>
 * <ul>
 *   <li>设置为高优先级（order = -1000），确保在 TraceId 拦截器（order = -2000）之后执行</li>
 *   <li>在 afterCompletion 阶段执行清理，确保无论请求成功还是异常都会清理</li>
 *   <li>此时 traceId 已经设置到 MDC 中，日志可以正常包含 traceId</li>
 * </ul>
 * 
 * <AUTHOR>
 * @since 2025-07-28
 */
@Slf4j
@Component
public class UserContextCleanupInterceptor implements HandlerInterceptor {

    /**
     * 请求开始前处理
     * 记录当前线程和用户信息，用于调试
     */
    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, 
                            @NonNull Object handler) {
        try {
            long threadId = Thread.currentThread().getId();
            String threadName = Thread.currentThread().getName();
            
            // 记录请求开始时的线程信息
            LogUtils.debug(log, "请求开始 - URI: {}, 线程ID: {}, 线程名: {}", 
                    request.getRequestURI(), threadId, threadName);
            
            // 检查是否有残留的用户上下文（这表明上一个请求没有正确清理）
            if (ThreadLocalCleanupUtil.hasRemainingUserContext()) {
                LogUtils.warn(log, "发现线程复用残留的用户上下文 - URI: {}, 线程ID: {}, 立即清理",
                        request.getRequestURI(), threadId);
                // 立即清理残留数据
                ThreadLocalCleanupUtil.performCompleteCleanup();
            }
            
            return true;
        } catch (Exception e) {
            LogUtils.error(log, "UserContextCleanupInterceptor preHandle 异常", e);
            // 即使异常也要继续处理请求
            return true;
        }
    }

    /**
     * 请求完成后处理（最重要的清理逻辑）
     * 无论请求成功还是异常，都会执行此方法
     * 强制清理所有可能的 ThreadLocal 数据
     */
    @Override
    public void afterCompletion(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
                               @NonNull Object handler, Exception ex) {
        try {
            long threadId = Thread.currentThread().getId();
            String threadName = Thread.currentThread().getName();
            
            // 记录清理前的用户信息（用于调试）
            boolean hasUserContext = false;
            try {
                Object context = ContextHolder.getContext();
                hasUserContext = (context != null);
            } catch (Exception e) {
                LogUtils.debug(log, "获取当前用户信息时发生异常", e);
            }
            
            // 执行强制清理
            ThreadLocalCleanupUtil.performCompleteCleanup();
            
            // 记录清理完成信息
            if (hasUserContext) {
                LogUtils.info(log, "用户上下文清理完成 - URI: {}, 线程ID: {}, 线程名: {} (有用户上下文)",
                        request.getRequestURI(), threadId, threadName);
            } else {
                LogUtils.debug(log, "用户上下文清理完成 - URI: {}, 线程ID: {}, 线程名: {} (无用户上下文)",
                        request.getRequestURI(), threadId, threadName);
            }
            
            // 如果有异常，记录异常信息
            if (ex != null) {
                LogUtils.warn(log, "请求处理异常，已执行用户上下文清理 - URI: {}, 异常: {}", 
                        request.getRequestURI(), ex.getClass().getSimpleName());
            }
            
        } catch (Exception e) {
            LogUtils.error(log, "UserContextCleanupInterceptor afterCompletion 清理异常", e);
        }
    }


}
