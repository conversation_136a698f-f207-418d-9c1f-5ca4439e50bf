package com.tem.customer.infrastructure.filter;

import cn.dev33.satoken.stp.StpUtil;
import com.iplatform.common.utils.LogUtils;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.serializer.SerializationException;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * Session反序列化异常处理过滤器
 * 用于捕获Spring Session在反序列化时的异常，并提供兜底处理
 * 
 * <AUTHOR>
 * @since 2025-07-28
 */
@Slf4j
@Component
@Order(-100) // 确保在SessionRepositoryFilter之前执行
public class SessionDeserializationExceptionFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                    FilterChain filterChain) throws ServletException, IOException {
        try {
            filterChain.doFilter(request, response);
        } catch (Exception e) {
            // 检查是否是Session反序列化异常
            if (isSessionDeserializationException(e)) {
                LogUtils.warn(log, "捕获到Session反序列化异常，尝试清理并使用Sa-Token兜底: {}", e.getMessage());
                
                // 清理有问题的Session Cookie
                clearSessionCookie(request, response);
                
                // 检查是否可以通过Sa-Token认证
                if (canAuthenticateWithSaToken(request)) {
                    LogUtils.info(log, "使用Sa-Token认证成功，继续处理请求");
                    // 重新执行过滤链，但这次没有有问题的session cookie
                    filterChain.doFilter(request, response);
                } else {
                    // 如果是需要登录的接口，返回未授权
                    if (!isPublicEndpoint(request)) {
                        LogUtils.warn(log, "Sa-Token认证失败，返回401");
                        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                        response.setContentType("application/json;charset=UTF-8");
                        response.getWriter().write("{\"code\":401,\"message\":\"认证失败，请重新登录\"}");
                    } else {
                        // 如果是公开接口（如登录页面），继续处理
                        filterChain.doFilter(request, response);
                    }
                }
            } else {
                // 不是Session反序列化异常，继续抛出
                throw e;
            }
        }
    }

    /**
     * 判断是否是Session反序列化异常
     */
    private boolean isSessionDeserializationException(Exception e) {
        Throwable cause = e;
        while (cause != null) {
            if (cause instanceof SerializationException) {
                return true;
            }
            if (cause.getMessage() != null && 
                (cause.getMessage().contains("Cannot deserialize") ||
                 cause.getMessage().contains("SerializationException") ||
                 cause.getMessage().contains("InvalidClassException"))) {
                return true;
            }
            cause = cause.getCause();
        }
        return false;
    }

    /**
     * 清理Session Cookie
     */
    private void clearSessionCookie(HttpServletRequest request, HttpServletResponse response) {
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                // 清理Spring Session的cookie（通常名为SESSION）
                if ("SESSION".equalsIgnoreCase(cookie.getName()) || 
                    "JSESSIONID".equalsIgnoreCase(cookie.getName())) {
                    Cookie clearCookie = new Cookie(cookie.getName(), "");
                    clearCookie.setPath("/");
                    clearCookie.setMaxAge(0);
                    clearCookie.setHttpOnly(true);
                    response.addCookie(clearCookie);
                    LogUtils.info(log, "清理Session Cookie: {}", cookie.getName());
                }
            }
        }
    }

    /**
     * 检查是否可以通过Sa-Token认证
     */
    private boolean canAuthenticateWithSaToken(HttpServletRequest request) {
        try {
            // 检查Sa-Token是否已登录
            return StpUtil.isLogin();
        } catch (Exception e) {
            LogUtils.debug(log, "Sa-Token认证检查失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 判断是否是公开接口
     */
    private boolean isPublicEndpoint(HttpServletRequest request) {
        String uri = request.getRequestURI();
        return uri.contains("/auth/") || 
               uri.contains("/login") ||
               uri.contains("/health-check") ||
               uri.contains("/qiyu/crm/");
    }
}
