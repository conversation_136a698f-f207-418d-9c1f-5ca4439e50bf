package com.tem.customer.controller.partner;

import com.iplatform.common.utils.LogUtils;
import com.tem.customer.shared.annotation.OperationLog;
import com.tem.customer.shared.common.Result;
import com.tem.customer.model.convert.PartnerWechatGroupConverter;
import com.tem.customer.model.dto.partner.PartnerWechatGroupDTO;
import com.tem.customer.repository.entity.PartnerWechatGroup;
import com.tem.customer.model.vo.partner.PartnerWechatGroupVO;
import com.tem.customer.shared.enums.BusinessType;
import com.tem.customer.shared.enums.OperationType;
import com.tem.customer.service.partner.PartnerWechatGroupService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 企业微信群绑定关系控制器
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Slf4j
@RestController
@RequestMapping("/api/cx/customer/admin/partner-wechat-groups")
@RequiredArgsConstructor
@Validated
public class PartnerWechatGroupController {

    private final PartnerWechatGroupService partnerWechatGroupService;

    /**
     * 根据企业ID查询微信群列表
     *
     * @param partnerId 企业ID
     * @return 微信群列表
     */
    @GetMapping("/partner/{partnerId}")
    public Result<List<PartnerWechatGroupVO>> listByPartnerId(@PathVariable @NotNull Long partnerId) {
        List<PartnerWechatGroup> groups = partnerWechatGroupService.listByPartnerId(partnerId);
        List<PartnerWechatGroupVO> voList = PartnerWechatGroupConverter.INSTANCE.toVOList(groups);
        return Result.success(voList);
    }

    /**
     * 根据企业ID查询启用状态的微信群列表
     *
     * @param partnerId 企业ID
     * @return 启用状态的微信群列表
     */
    @GetMapping("/partner/{partnerId}/enabled")
    public Result<List<PartnerWechatGroupVO>> listEnabledByPartnerId(@PathVariable @NotNull Long partnerId) {
        List<PartnerWechatGroup> groups = partnerWechatGroupService.listEnabledByPartnerId(partnerId);
        List<PartnerWechatGroupVO> voList = PartnerWechatGroupConverter.INSTANCE.toVOList(groups);
        return Result.success(voList);
    }

    /**
     * 根据ID查询微信群详情
     *
     * @param id 记录ID
     * @return 微信群详情
     */
    @GetMapping("/{id}")
    public Result<PartnerWechatGroupVO> getById(@PathVariable @NotNull Long id) {
        PartnerWechatGroup group = partnerWechatGroupService.getById(id);
        if (group == null) {
            return Result.error("企业微信群绑定关系不存在");
        }
        PartnerWechatGroupVO vo = PartnerWechatGroupConverter.INSTANCE.toVO(group);
        return Result.success(vo);
    }

    /**
     * 根据chatId查询微信群详情
     *
     * @param chatId 企业微信群ID
     * @return 微信群详情，包含企业名称
     */
    @GetMapping("/chat/{chatId}")
    public Result<PartnerWechatGroupVO> getByChatId(@PathVariable @NotNull String chatId) {
        PartnerWechatGroupVO vo = partnerWechatGroupService.getByChatIdVO(chatId);
        if (vo == null) {
            return Result.error("企业微信群绑定关系不存在");
        }
        return Result.success(vo);
    }

    /**
     * 创建企业微信群绑定关系
     *
     * @param dto 企业微信群绑定关系DTO
     * @return 创建结果
     */
    @PostMapping
    @OperationLog(businessType = BusinessType.PARTNER_WECHAT_GROUP, operationType = OperationType.CREATE,
            description = "创建企业微信群绑定关系",
            businessIdExpression = "#dto.partnerId",
            targetPartnerIdExpression = "#dto.partnerId")
    public Result<PartnerWechatGroupVO> create(@RequestBody @Valid PartnerWechatGroupDTO dto) {
        PartnerWechatGroup group = PartnerWechatGroupConverter.INSTANCE.toEntity(dto);
        PartnerWechatGroup partnerWechatGroup = partnerWechatGroupService.createPartnerWechatGroup(group);
        if(null == partnerWechatGroup){
            LogUtils.info(log, "企微群绑定企业失败");
        }
        PartnerWechatGroupVO partnerWechatGroupVO = PartnerWechatGroupConverter.INSTANCE.toVO(partnerWechatGroup);
        return Result.success(partnerWechatGroupVO);
    }

    /**
     * 更新企业微信群绑定关系
     *
     * @param id  记录ID
     * @param dto 企业微信群绑定关系DTO
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @OperationLog(businessType = BusinessType.PARTNER_WECHAT_GROUP, operationType = OperationType.UPDATE,
            description = "更新企业微信群绑定关系",
            businessIdExpression = "#id",
            targetPartnerIdExpression = "#dto.partnerId")
    public Result<Void> update(@PathVariable @NotNull Long id, @RequestBody @Valid PartnerWechatGroupDTO dto) {
        dto.setId(id);
        PartnerWechatGroup group = PartnerWechatGroupConverter.INSTANCE.toEntity(dto);
        boolean success = partnerWechatGroupService.updatePartnerWechatGroup(group);
        return success ? Result.success() : Result.error("更新失败");
    }

    /**
     * 删除企业微信群绑定关系
     *
     * @param id 记录ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @OperationLog(businessType = BusinessType.PARTNER_WECHAT_GROUP, operationType = OperationType.DELETE,
            description = "删除企业微信群绑定关系",
            businessIdExpression = "#id")
    public Result<Void> delete(@PathVariable @NotNull Long id) {
        boolean success = partnerWechatGroupService.deletePartnerWechatGroup(id);
        return success ? Result.success() : Result.error("删除失败");
    }

    /**
     * 启用/禁用企业微信群
     *
     * @param id     记录ID
     * @param status 状态：1-启用，0-禁用
     * @return 操作结果
     */
    @PutMapping("/{id}/status")
    @OperationLog(businessType = BusinessType.PARTNER_WECHAT_GROUP, operationType = OperationType.UPDATE,
            description = "更新企业微信群状态",
            businessIdExpression = "#id")
    public Result<Void> updateStatus(@PathVariable @NotNull Long id, @RequestParam @NotNull Integer status) {
        boolean success = partnerWechatGroupService.updateStatus(id, status);
        return success ? Result.success() : Result.error("操作失败");
    }

    /**
     * 调整企业微信群排序
     *
     * @param id        记录ID
     * @param sortOrder 新的排序值
     * @return 操作结果
     */
    @PutMapping("/{id}/sort")
    @OperationLog(businessType = BusinessType.PARTNER_WECHAT_GROUP, operationType = OperationType.UPDATE,
            description = "调整企业微信群排序",
            businessIdExpression = "#id")
    public Result<Void> updateSortOrder(@PathVariable @NotNull Long id, @RequestParam @NotNull Integer sortOrder) {
        boolean success = partnerWechatGroupService.updateSortOrder(id, sortOrder);
        return success ? Result.success() : Result.error("操作失败");
    }

    /**
     * 检查chatId是否已存在
     *
     * @param chatId    企业微信群ID
     * @param excludeId 排除的记录ID（更新时使用）
     * @return 是否存在
     */
    @GetMapping("/check-chat-id")
    public Result<Boolean> checkChatIdExists(@RequestParam @NotNull String chatId,
                                             @RequestParam(required = false) Long excludeId) {
        boolean exists = partnerWechatGroupService.existsByChatId(chatId, excludeId);
        return Result.success(exists);
    }
}
