package com.tem.customer.service.impl

import com.tem.customer.repository.entity.PartnerWechatGroup
import com.tem.customer.shared.exception.BusinessException
import com.tem.customer.repository.mapper.PartnerWechatGroupMapper
import com.tem.customer.service.partner.PartnerWechatGroupServiceImpl
import spock.lang.Specification
import spock.lang.Subject

/**
 * 企业微信群绑定关系服务实现类测试
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
class PartnerWechatGroupServiceImplSpec extends Specification {

    @Subject
    PartnerWechatGroupServiceImpl service

    PartnerWechatGroupMapper mapper = Mock()

    def setup() {
        service = new PartnerWechatGroupServiceImpl()
        service.baseMapper = mapper
    }

    def "测试根据企业ID查询微信群列表 - 正常情况"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def groups = [
                createPartnerWechatGroup(1L, partnerId, "chat1", 1, 1),
                createPartnerWechatGroup(2L, partnerId, "chat2", 1, 2)
        ]

        when: "调用查询方法"
        def result = service.listByPartnerId(partnerId)

        then: "验证结果"
        1 * mapper.selectByPartnerIdOrderBySort(partnerId) >> groups
        result == groups
        result.size() == 2
    }

    def "测试根据企业ID查询微信群列表 - 企业ID为空"() {
        when: "传入空的企业ID"
        def result = service.listByPartnerId(null)

        then: "返回空列表"
        0 * mapper.selectByPartnerIdOrderBySort(_)
        result.isEmpty()
    }

    def "测试根据企业ID查询启用状态的微信群列表"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def enabledGroups = [
                createPartnerWechatGroup(1L, partnerId, "chat1", "测试群1", 1, 1)
        ]

        when: "调用查询启用状态的方法"
        def result = service.listEnabledByPartnerId(partnerId)

        then: "验证结果"
        1 * mapper.selectEnabledByPartnerIdOrderBySort(partnerId) >> enabledGroups
        result == enabledGroups
        result.size() == 1
    }

    def "测试统计企业微信群数量"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def count = 3

        when: "调用统计方法"
        def result = service.countByPartnerId(partnerId)

        then: "验证结果"
        1 * mapper.countByPartnerId(partnerId) >> count
        result == count
    }

    def "测试根据chatId查询微信群信息"() {
        given: "准备测试数据"
        def chatId = "chat123"
        def group = createPartnerWechatGroup(1L, 1001L, chatId, "测试群", 1, 1)

        when: "调用查询方法"
        def result = service.getByChatId(chatId)

        then: "验证结果"
        1 * mapper.selectByChatId(chatId) >> group
        result == group
    }

    def "测试根据chatId查询微信群信息 - chatId为空"() {
        when: "传入空的chatId"
        def result = service.getByChatId(null)

        then: "返回null"
        0 * mapper.selectByChatId(_)
        result == null
    }

    def "测试创建企业微信群绑定关系 - 成功"() {
        given: "准备测试数据"
        def group = createPartnerWechatGroup(null, 1001L, "chat123", "测试群", null, null)
        
        and: "Mock依赖方法"
        mapper.countByPartnerId(1001L) >> 2
        mapper.existsByChatIdExcludeId("chat123", null) >> false
        mapper.getMaxSortOrderByPartnerId(1001L) >> 2
        mapper.insert(group) >> 1

        when: "调用创建方法"
        def result = service.createPartnerWechatGroup(group)

        then: "验证结果"
        result
        group.status == 1
        group.sortOrder == 3
        group.groupType == "CUSTOMER_SERVICE"
    }

    def "测试创建企业微信群绑定关系 - 数量超限"() {
        given: "准备测试数据"
        def group = createPartnerWechatGroup(null, 1001L, "chat123", "测试群", null, null)
        
        and: "Mock数量已达上限"
        mapper.countByPartnerId(1001L) >> 10

        when: "调用创建方法"
        service.createPartnerWechatGroup(group)

        then: "抛出业务异常"
        def ex = thrown(BusinessException)
        ex.message.contains("数量已达到上限")
    }

    def "测试创建企业微信群绑定关系 - chatId已存在"() {
        given: "准备测试数据"
        def group = createPartnerWechatGroup(null, 1001L, "chat123", "测试群", null, null)
        
        and: "Mock依赖方法"
        mapper.countByPartnerId(1001L) >> 2
        mapper.existsByChatIdExcludeId("chat123", null) >> true

        when: "调用创建方法"
        service.createPartnerWechatGroup(group)

        then: "抛出业务异常"
        def ex = thrown(BusinessException)
        ex.message.contains("已存在")
    }

    def "测试创建企业微信群绑定关系 - 参数校验失败"() {
        when: "传入无效参数"
        service.createPartnerWechatGroup(invalidGroup)

        then: "抛出业务异常"
        def ex = thrown(BusinessException)
        ex.message.contains(expectedMessage)

        where:
        invalidGroup                                                    | expectedMessage
        null                                                           | "不能为空"
        createPartnerWechatGroup(null, null, "chat123", "测试群", null, null) | "企业ID不能为空"
        createPartnerWechatGroup(null, 1001L, null, "测试群", null, null)     | "企业微信群ID不能为空"
        createPartnerWechatGroup(null, 1001L, "chat123", null, null, null)   | "群名称不能为空"
    }

    def "测试更新企业微信群绑定关系 - 成功"() {
        given: "准备测试数据"
        def group = createPartnerWechatGroup(1L, 1001L, "chat123", "更新后的群名", 1, 1)
        def existingGroup = createPartnerWechatGroup(1L, 1001L, "chat123", "原群名", 1, 1)
        
        and: "Mock依赖方法"
        mapper.selectById(1L) >> existingGroup
        mapper.existsByChatIdExcludeId("chat123", 1L) >> false
        mapper.updateById(group) >> 1

        when: "调用更新方法"
        def result = service.updatePartnerWechatGroup(group)

        then: "验证结果"
        result
    }

    def "测试更新企业微信群绑定关系 - 记录不存在"() {
        given: "准备测试数据"
        def group = createPartnerWechatGroup(1L, 1001L, "chat123", "测试群", 1, 1)
        
        and: "Mock记录不存在"
        mapper.selectById(1L) >> null

        when: "调用更新方法"
        service.updatePartnerWechatGroup(group)

        then: "抛出业务异常"
        def ex = thrown(BusinessException)
        ex.message.contains("不存在")
    }

    def "测试删除企业微信群绑定关系 - 成功"() {
        given: "准备测试数据"
        def id = 1L
        def existingGroup = createPartnerWechatGroup(id, 1001L, "chat123", "测试群", 1, 1)
        
        and: "Mock依赖方法"
        mapper.selectById(id) >> existingGroup
        mapper.deleteById(id) >> 1

        when: "调用删除方法"
        def result = service.deletePartnerWechatGroup(id)

        then: "验证结果"
        result
    }

    def "测试更新状态"() {
        given: "准备测试数据"
        def id = 1L
        def status = 0
        
        and: "Mock更新成功"
        mapper.updateById(_) >> 1

        when: "调用更新状态方法"
        def result = service.updateStatus(id, status)

        then: "验证结果"
        result
    }

    def "测试更新排序"() {
        given: "准备测试数据"
        def id = 1L
        def sortOrder = 5
        
        and: "Mock更新成功"
        mapper.updateById(_) >> 1

        when: "调用更新排序方法"
        def result = service.updateSortOrder(id, sortOrder)

        then: "验证结果"
        result
    }

    def "测试检查chatId是否存在"() {
        given: "准备测试数据"
        def chatId = "chat123"
        def excludeId = 1L
        
        when: "调用检查方法"
        def result = service.existsByChatId(chatId, excludeId)

        then: "验证结果"
        1 * mapper.existsByChatIdExcludeId(chatId, excludeId) >> true
        result
    }

    /**
     * 创建测试用的PartnerWechatGroup对象
     */
    private static PartnerWechatGroup createPartnerWechatGroup(Long id, Long partnerId, String chatId, Integer status, Integer sortOrder) {
        PartnerWechatGroup group = new PartnerWechatGroup()
        group.id = id
        group.partnerId = partnerId
        group.chatId = chatId
        group.groupType = "CUSTOMER_SERVICE"
        group.status = status
        group.sortOrder = sortOrder
        group.remark = "测试备注"
        return group
    }
}
