package com.tem.customer.service.partner

import com.iplatform.common.ResponseDto
import com.tem.customer.model.vo.partner.WechatUserBindingVO
import com.tem.customer.repository.entity.PartnerWechatGroup
import com.tem.customer.repository.entity.WechatUserBinding
import com.tem.customer.repository.mapper.WechatUserBindingMapper
import com.tem.customer.shared.exception.BusinessException
import com.tem.platform.api.PartnerService
import com.tem.platform.api.UserService
import com.tem.platform.api.dto.PartnerDto
import com.tem.platform.api.dto.UserDto
import spock.lang.Specification
import spock.lang.Subject

/**
 * 微信用户绑定关系服务实现类测试
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
class WechatUserBindingServiceImplSpec extends Specification {

    @Subject
    WechatUserBindingServiceImpl service

    WechatUserBindingMapper mapper = Mock()
    PartnerWechatGroupService partnerWechatGroupService = Mock()
    UserService userService = Mock()
    PartnerService partnerService = Mock()

    def setup() {
        service = new WechatUserBindingServiceImpl(partnerWechatGroupService)
        service.baseMapper = mapper
        service.userService = userService
        service.partnerService = partnerService
    }

    def "测试根据企业ID查询绑定关系列表 - 正常情况"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def bindings = [
                createWechatUserBinding(1L, partnerId, 2001L, "union1", "chat1", "WORK_WECHAT"),
                createWechatUserBinding(2L, partnerId, 2002L, "union2", "chat2", "WORK_WECHAT")
        ]

        when: "调用查询方法"
        def result = service.listByPartnerId(partnerId)

        then: "验证结果"
        1 * mapper.selectByPartnerId(partnerId) >> bindings
        result == bindings
        result.size() == 2
    }

    def "测试根据企业ID查询绑定关系列表 - 企业ID为空"() {
        when: "传入空的企业ID"
        def result = service.listByPartnerId(null)

        then: "返回空列表"
        0 * mapper.selectByPartnerId(_)
        result.isEmpty()
    }

    def "测试根据企业ID和用户ID查询绑定关系"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def userId = 2001L
        def bindings = [
                createWechatUserBinding(1L, partnerId, userId, "union1", "chat1", "WORK_WECHAT")
        ]

        when: "调用查询方法"
        def result = service.listByPartnerIdAndUserId(partnerId, userId)

        then: "验证结果"
        1 * mapper.selectByPartnerIdAndUserId(partnerId, userId) >> bindings
        result == bindings
        result.size() == 1
    }

    def "测试根据UnionID查询绑定关系"() {
        given: "准备测试数据"
        def unionId = "union123"
        def binding = createWechatUserBinding(1L, 1001L, 2001L, unionId, "chat1", "WORK_WECHAT")

        when: "调用查询方法"
        def result = service.getByUnionId(unionId)

        then: "验证结果"
        1 * mapper.selectByUnionId(unionId) >> binding
        result == binding
    }

    def "测试根据来源类型查询绑定关系列表"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def sourceType = "WORK_WECHAT"
        def bindings = [
                createWechatUserBinding(1L, partnerId, 2001L, "union1", "chat1", sourceType)
        ]

        when: "调用查询方法"
        def result = service.listBySourceType(partnerId, sourceType)

        then: "验证结果"
        1 * mapper.selectBySourceType(partnerId, sourceType) >> bindings
        result == bindings
        result.size() == 1
    }

    def "测试统计绑定关系数量"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def count = 3

        when: "调用统计方法"
        def result = service.countByPartnerId(partnerId)

        then: "验证结果"
        1 * mapper.countByPartnerId(partnerId) >> count
        result == count
    }

    def "测试统计有效绑定关系数量"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def count = 2

        when: "调用统计有效绑定关系方法"
        def result = service.countValidByPartnerId(partnerId)

        then: "验证结果"
        1 * mapper.countValidByPartnerId(partnerId) >> count
        result == count
    }

    def "测试创建微信用户绑定关系 - 成功"() {
        given: "准备测试数据"
        def binding = createWechatUserBinding(null, 1001L, 2001L, "union123", "chat1", "WORK_WECHAT")
        
        and: "Mock依赖方法"
        mapper.existsByUnionIdExcludeId(1001L, "union123", null) >> false
        partnerWechatGroupService.getByChatId("chat1") >> createPartnerWechatGroup(1L, 1001L, "chat1", "测试群", 1, 1)
        mapper.existsByChatIdAndUserIdExcludeId("chat1", 2001L, null) >> false
        mapper.insert(binding) >> 1

        when: "调用创建方法"
        def result = service.createWechatUserBinding(binding)

        then: "验证结果"
        result
        binding.status == 1
    }

    def "测试创建微信用户绑定关系 - UnionID已存在"() {
        given: "准备测试数据"
        def binding = createWechatUserBinding(null, 1001L, 2001L, "union123", "chat1", "WORK_WECHAT")
        
        and: "Mock UnionID已存在"
        mapper.existsByUnionIdExcludeId(1001L, "union123", null) >> true

        when: "调用创建方法"
        service.createWechatUserBinding(binding)

        then: "抛出业务异常"
        def ex = thrown(BusinessException)
        ex.message.contains("UnionID已存在")
    }

    def "测试创建微信用户绑定关系 - 参数校验失败"() {
        when: "传入无效参数"
        service.createWechatUserBinding(invalidBinding)

        then: "抛出业务异常"
        def ex = thrown(BusinessException)
        ex.message.contains(expectedMessage)

        where:
        invalidBinding | expectedMessage
        null           | "不能为空"
        createWechatUserBinding(null, null, 2001L, "union123", "chat1", "WORK_WECHAT") | "企业ID不能为空"
        createWechatUserBinding(null, 1001L, 2001L, null, null, "WORK_WECHAT") | "至少需要提供一个微信标识"
        createWechatUserBinding(null, 1001L, 2001L, null, null, "INVALID_TYPE") | "无效的来源类型"
    }

    def "测试更新微信用户绑定关系 - 成功"() {
        given: "准备测试数据"
        def binding = createWechatUserBinding(1L, 1001L, 2001L, "union123", "chat1", "WORK_WECHAT")
        def existingBinding = createWechatUserBinding(1L, 1001L, 2001L, "union123", "chat1", "WORK_WECHAT")
        
        and: "Mock依赖方法"
        mapper.selectById(1L) >> existingBinding
        mapper.existsByUnionIdExcludeId(1001L, "union123", 1L) >> false
        partnerWechatGroupService.getByChatId("chat1") >> createPartnerWechatGroup(1L, 1001L, "chat1", "测试群", 1, 1)
        mapper.existsByChatIdAndUserIdExcludeId("chat1", 2001L, 1L) >> false
        mapper.updateById(binding) >> 1

        when: "调用更新方法"
        def result = service.updateWechatUserBinding(binding)

        then: "验证结果"
        result
    }

    def "测试更新微信用户绑定关系 - 记录不存在"() {
        given: "准备测试数据"
        def binding = createWechatUserBinding(1L, 1001L, 2001L, "union123", "chat1", "WORK_WECHAT")
        
        and: "Mock记录不存在"
        mapper.selectById(1L) >> null

        when: "调用更新方法"
        service.updateWechatUserBinding(binding)

        then: "抛出业务异常"
        def ex = thrown(BusinessException)
        ex.message.contains("不存在")
    }

    def "测试删除微信用户绑定关系 - 成功"() {
        given: "准备测试数据"
        def id = 1L
        def existingBinding = createWechatUserBinding(id, 1001L, 2001L, "union123", "chat1", "WORK_WECHAT")
        
        and: "Mock依赖方法"
        mapper.selectById(id) >> existingBinding
        mapper.deleteById(id) >> 1

        when: "调用删除方法"
        def result = service.deleteWechatUserBinding(id)

        then: "验证结果"
        result
    }

    def "测试更新状态 - 成功"() {
        given: "准备测试数据"
        def id = 1L
        def status = 0
        def existingBinding = createWechatUserBinding(id, 1001L, 2001L, "union123", "chat1", "WORK_WECHAT")
        
        and: "Mock依赖方法"
        mapper.selectById(id) >> existingBinding
        mapper.updateById(_) >> 1

        when: "调用更新状态方法"
        def result = service.updateStatus(id, status)

        then: "验证结果"
        result
    }

    def "测试创建群内人员绑定关系 - 成功"() {
        given: "准备测试数据"
        def binding = createWechatUserBinding(null, 1001L, 2001L, null, "chat1", "GROUP_STAFF")
        
        and: "Mock依赖方法"
        partnerWechatGroupService.getByChatId("chat1") >> createPartnerWechatGroup(1L, 1001L, "chat1", "测试群", 1, 1)
        mapper.existsByChatIdAndUserIdExcludeId("chat1", 2001L, null) >> false
        mapper.insert(binding) >> 1

        when: "调用创建群内人员绑定方法"
        def result = service.createGroupStaffBinding(binding)

        then: "验证结果"
        result
        binding.status == 1
        binding.sourceType == "GROUP_STAFF"
    }

    def "测试创建群内人员绑定关系 - 微信群不存在"() {
        given: "准备测试数据"
        def binding = createWechatUserBinding(null, 1001L, 2001L, null, "chat1", "GROUP_STAFF")
        
        and: "Mock微信群不存在"
        partnerWechatGroupService.getByChatId("chat1") >> null

        when: "调用创建群内人员绑定方法"
        service.createGroupStaffBinding(binding)

        then: "抛出业务异常"
        def ex = thrown(BusinessException)
        ex.message.contains("微信群不存在")
    }

    def "测试根据微信群ID查询绑定关系列表 - 正常情况"() {
        given: "准备测试数据"
        def chatId = "chat123"
        def bindings = [
                createWechatUserBinding(1L, 1001L, 2001L, "union1", chatId, "WORK_WECHAT")
        ]

        and: "Mock依赖方法"
        mapper.selectByChatId(chatId) >> bindings
        userService.getUsers([2001L]) >> ResponseDto.success([createUserDto(2001L, "测试用户")])
        partnerService.findByBpIds([1001L]) >> ResponseDto.success([createPartnerDto(1001L, "测试企业")])

        when: "调用查询方法"
        def result = service.listByChatIdWithGroupName(chatId)

        then: "验证结果"
        result.size() == 1
        result[0].chatId == chatId
        result[0].userName == "测试用户"
        result[0].partnerName == "测试企业"
    }

    def "测试根据UnionID全局查询绑定关系"() {
        given: "准备测试数据"
        def unionId = "union123"
        def binding = createWechatUserBinding(1L, 1001L, 2001L, unionId, "chat1", "WORK_WECHAT")

        when: "调用全局查询方法"
        def result = service.getByUnionIdGlobal(unionId)

        then: "验证结果"
        1 * mapper.selectByUnionIdGlobal(unionId) >> binding
        result == binding
    }

    def "测试根据UnionID查询绑定关系并填充完整信息"() {
        given: "准备测试数据"
        def unionId = "union123"
        def binding = createWechatUserBinding(1L, 1001L, 2001L, unionId, "chat1", "WORK_WECHAT")
        
        and: "Mock依赖方法"
        mapper.selectByUnionId(unionId) >> binding
        userService.getUser(2001L) >> ResponseDto.success(createUserDto(2001L, "测试用户"))
        partnerService.findById(1001L) >> ResponseDto.success(createPartnerDto(1001L, "测试企业"))

        when: "调用查询完整信息方法"
        def result = service.getByUnionIdWithFullInfo(unionId)

        then: "验证结果"
        result
        result.unionId == unionId
        result.userName == "测试用户"
        result.partnerName == "测试企业"
    }

    def "测试检查chatId和用户绑定关系是否已存在"() {
        given: "准备测试数据"
        def chatId = "chat123"
        def userId = 2001L
        def excludeId = 1L

        when: "调用检查方法"
        def result = service.existsByChatIdAndUserId(chatId, userId, excludeId)

        then: "验证结果"
        1 * mapper.existsByChatIdAndUserIdExcludeId(chatId, userId, excludeId) >> true
        result
    }

    /**
     * 创建测试用的WechatUserBinding对象
     */
    private static WechatUserBinding createWechatUserBinding(Long id, Long partnerId, Long userId, String unionId, String chatId, String sourceType) {
        WechatUserBinding binding = new WechatUserBinding()
        binding.id = id
        binding.partnerId = partnerId
        binding.userId = userId
        binding.unionId = unionId
        binding.chatId = chatId
        binding.sourceType = sourceType
        binding.status = 1
        binding.remark = "测试备注"
        return binding
    }

    /**
     * 创建测试用的PartnerWechatGroup对象
     */
    private static PartnerWechatGroup createPartnerWechatGroup(Long id, Long partnerId, String chatId, String groupName, Integer status, Integer sortOrder) {
        PartnerWechatGroup group = new PartnerWechatGroup()
        group.id = id
        group.partnerId = partnerId
        group.chatId = chatId
        group.groupName = groupName
        group.groupType = "CUSTOMER_SERVICE"
        group.status = status
        group.sortOrder = sortOrder
        return group
    }

    /**
     * 创建测试用的UserDto对象
     */
    private static UserDto createUserDto(Long id, String name) {
        UserDto userDto = new UserDto()
        userDto.id = id
        userDto.fullname = name
        userDto.mobile = "13800138000"
        userDto.email = "<EMAIL>"
        return userDto
    }

    /**
     * 创建测试用的PartnerDto对象
     */
    private static PartnerDto createPartnerDto(Long id, String name) {
        PartnerDto partnerDto = new PartnerDto()
        partnerDto.id = id
        partnerDto.name = name
        return partnerDto
    }
}